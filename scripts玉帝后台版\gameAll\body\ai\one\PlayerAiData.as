package gameAll.body.ai.one
{
   import com.sounto.utils.ClassProperty;
   
   public class PlayerAiData extends HeroAiData
   {
      
      public static var pro_arr:Array = null;
      
      public var lifeBottle:Number = 0.4;
      
      public var pBottle:Number = 0.4;
      
      public var caissonB:Boolean = true;
      
      public var findCaissonB:Boolean = true;
      
      public var restartB:Boolean = false;
      
      public var autoSaveB:Boolean = true;
      
      public function PlayerAiData()
      {
         super();
      }
      
      override public function inData_byObj(obj0:Object) : void
      {
         ClassProperty.inData_bySaveObj(this,obj0,pro_arr);
      }
      
      override protected function getFun(name0:*) : Function
      {
         return this[name0];
      }
   }
}

