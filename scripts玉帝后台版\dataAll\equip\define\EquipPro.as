package dataAll.equip.define
{
   import dataAll.arms.define.ArmsType;
   
   public class EquipPro
   {
      
      public static const dayLoveAdd:String = "dayLoveAdd";
      
      public static const sweepingNum:String = "sweepingNum";
      
      public static const demStroneDropNum:String = "demStroneDropNum";
      
      public static const demBallDropNum:String = "demBallDropNum";
      
      public static const madheartDropNum:String = "madheartDropNum";
      
      public static const dpsArr:Array = ["attackGap","critPro3","dpsWhole","dps","dpsMul","dpsAll","dpsAllBlack","dpsVip","hurt","hurtMul","hurtAll"];
      
      public static const onlyArr:Array = ["damageMul","cdMul"];
      
      public static const armsDpsArr:Array = ["zodiacArmsHurtAdd"];
      
      public static const armsOtherArr:Array = ["capacity","capacityMul","capacityMulBlack","charger","chargerMul","chargerMulBlack","reload"];
      
      public static const lifeArr:Array = ["life","lifeMul","lifeAll","lifeAllBlack","lifeVip","lifeRate","lifeRateBlack","lifeRateMul","head","headMul","headVip","dodge","fightDedut","bulletDedut","skillDedut"];
      
      public static const motionArr:Array = ["moveMul","maxJumpNumAdd","fgNE"];
      
      public static const moreArr:Array = ["moreDpsMul","moreLifeMul","vehicleDefMul","vehicleDpsMul"];
      
      private static var normalArr:Array = null;
      
      public static var dropNameArr:Array = [sweepingNum,demStroneDropNum,demBallDropNum,madheartDropNum,"blackArmsDropPro","blackEquipDropPro","ranBlackArmsDropPro","rareArmsDropPro","rareEquipDropPro","specialPartsDropPro","gemDropPro","petBookDropPro","rareGeneDropPro","loveAdd",dayLoveAdd];
      
      public static const dropArr:Array = ["lottery","exp","expMul","expVip",sweepingNum,demStroneDropNum,demBallDropNum,madheartDropNum,"blackArmsDropPro","blackEquipDropPro","ranBlackArmsDropPro","rareArmsDropPro","rareEquipDropPro","specialPartsDropPro","gemDropPro","petBookDropPro","rareGeneDropPro","loveAdd",dayLoveAdd,"weaponDropPro","deviceDropPro","bloodStoneDropPro","arenaStampDropNum","vehicleCashDropNum","lifeCatalystDropPro","godStoneDropPro","converStoneDropPro","taxStampDropPro","coinMul"];
      
      public static const more50DropArr:Array = ["petBookDropPro","rareGeneDropPro","blackArmsDropPro","blackEquipDropPro","gemDropPro","specialPartsDropPro","rareArmsDropPro","rareEquipDropPro","weaponDropPro","deviceDropPro","bloodStoneDropPro","lifeCatalystDropPro","godStoneDropPro","converStoneDropPro","taxStampDropPro"];

      public static const more100DropArr:Array = ["exp","expMul","expVip","coinMul","lottery"];

      public static const bagDropArr:Array = [dayLoveAdd,sweepingNum,demStroneDropNum,demBallDropNum,madheartDropNum,"loveAdd","arenaStampDropNum","vehicleCashDropNum"];
      
      public static const lotteryDropArr:Array = ["taxStampDropPro","blackEquipDropPro","blackArmsDropPro","ranBlackArmsDropPro","bloodStoneDropPro","deviceDropPro","weaponDropPro","converStoneDropPro","godStoneDropPro","lifeCatalystDropPro","rareEquipDropPro","rareArmsDropPro","orredEquipDropPro","orredArmsDropPro"];
      
      public static const startArr:Array = onlyArr.concat(armsOtherArr).concat(moreArr).concat(["dodge","fightDedut","bulletDedut","skillDedut"]);
      
      public static var noProNoShowArr:Array = null;
      
      public static const DPS:String = "dps";
      
      public static const ONLY:String = "only";
      
      public static const ARMS_DPS:String = "armsDps";
      
      public static const ARMS_OTHER:String = "armsOther";
      
      public static const LIFE:String = "life";
      
      public static const MOTION:String = "motion";
      
      public static const MORE:String = "more";
      
      public static const DROP:String = "drop";
      
      public function EquipPro()
      {
         super();
      }
      
      public static function isMore50(pro0:String) : Boolean
      {
         return more50DropArr.indexOf(pro0) >= 0;
      }
      
      public static function isBag(pro0:String) : Boolean
      {
         return bagDropArr.indexOf(pro0) >= 0;
      }
      
      public static function staticInit() : void
      {
         var type0:String = null;
         var pro0:String = null;
         var armsTypeArr0:Array = ArmsType.TYPE_ARR;
         var proArr0:Array = ["dpsMul","hurtMul"];
         for each(type0 in armsTypeArr0)
         {
            for each(pro0 in proArr0)
            {
               armsDpsArr.push(pro0 + "_" + type0);
            }
         }
         noProNoShowArr = armsDpsArr.concat(dropArr);
      }
      
      public static function getNormalArr() : Array
      {
         if(normalArr == null)
         {
            normalArr = dpsArr;
            normalArr = normalArr.concat(onlyArr);
            normalArr = normalArr.concat(armsDpsArr);
            normalArr = normalArr.concat(armsOtherArr);
            normalArr = normalArr.concat(lifeArr);
            normalArr = normalArr.concat(motionArr);
            normalArr = normalArr.concat(moreArr);
         }
         return normalArr;
      }
      
      public static function getDropArr() : Array
      {
         return dropArr;
      }
      
      public static function getFather(name0:String) : String
      {
         if(dpsArr.indexOf(name0) >= 0)
         {
            return DPS;
         }
         if(onlyArr.indexOf(name0) >= 0)
         {
            return ONLY;
         }
         if(armsDpsArr.indexOf(name0) >= 0)
         {
            return ARMS_DPS;
         }
         if(armsOtherArr.indexOf(name0) >= 0)
         {
            return ARMS_OTHER;
         }
         if(lifeArr.indexOf(name0) >= 0)
         {
            return LIFE;
         }
         if(motionArr.indexOf(name0) >= 0)
         {
            return MOTION;
         }
         if(moreArr.indexOf(name0) >= 0)
         {
            return MORE;
         }
         if(dropArr.indexOf(name0) >= 0)
         {
            return DROP;
         }
         return "";
      }
      
      public static function getSumMax(name0:String) : Number
      {
         if(name0 == sweepingNum)
         {
            return 3 * 3;
         }
         if(name0 == demStroneDropNum)
         {
            return 2;
         }
         if(name0 == demBallDropNum)
         {
            return 4;
         }
         if(name0 == madheartDropNum)
         {
            return 3;
         }
         if(name0 == dayLoveAdd)
         {
            return 40;
         }
         return 0;
      }
      
      public static function getMoreAddMul(name0:String) : Number
      {
         if(Boolean(name0.indexOf("DropPro")))
         {
            return 0.5;
         }
         return 1;
      }
      
      public static function getDodgeMax() : Number
      {
         return 0.5;
      }
      
      public static function getFightDedutMax() : Number
      {
         return 0.65;
      }
      
      public static function getSkillDedutMax() : Number
      {
         return 0.9;
      }
      
      public static function getBulletDedutMax() : Number
      {
         return 0.85;
      }
   }
}

